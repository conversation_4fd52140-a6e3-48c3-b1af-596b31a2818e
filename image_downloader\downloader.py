import os
import requests
from bs4 import BeautifulSoup
from PySide6.QtCore import QObject, Signal
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock

from .logger import logger
from .utils import (
    clean_filename,
    create_save_directory,
    extract_url_from_text
)
from .image_processor import ImageProcessor

class ImageDownloader(QObject):
    progress_updated = Signal(str, int, int)  # 网页URL, 当前进度, 总数
    download_completed = Signal(str, str)  # 网页URL, 保存路径
    download_error = Signal(str, str)  # 网页URL, 错误信息

    def __init__(self, max_workers=5):
        super().__init__()
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        self.image_processor = ImageProcessor()
        self.max_workers = max_workers
        self.progress_lock = Lock()
        self.current_progress = 0

    def download_image(self, img_url, save_path, index, total_images, save_format):
        """下载单个图片"""
        try:
            logger.info(f"开始下载图片 {index}/{total_images}: {img_url}")
            response = requests.get(img_url, headers=self.headers, stream=True)
            response.raise_for_status()

            # 获取原始图片数据
            image_data = response.content

            # 转换图片格式
            if save_format:
                converted_data = self.image_processor.convert_format(image_data, save_format)
                if converted_data:
                    image_data = converted_data

            # 生成文件名
            filename = f"{index:03d}.{save_format.lower()}"
            filepath = os.path.join(save_path, filename)

            # 保存图片
            with open(filepath, "wb") as f:
                f.write(image_data)
            logger.info(f"图片保存成功: {filepath}")
            return True
        except Exception as e:
            logger.error(f"下载图片失败: {img_url}, 错误: {str(e)}")
            return False

    def update_progress(self, url, total_images):
        """更新下载进度"""
        with self.progress_lock:
            self.current_progress += 1
            self.progress_updated.emit(url, self.current_progress, total_images)

    def download_from_url(self, url, save_dir, excluded_formats, save_format, download_count=1):
        """从URL下载所有图片"""
        try:
            # 重置进度
            self.current_progress = 0

            # 提取URL
            url = extract_url_from_text(url)
            logger.info(f"开始从URL下载图片: {url}")

            # 获取网页内容
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, "lxml")

            # 获取页面标题
            title = soup.title.string if soup.title else "未命名"
            title = clean_filename(title)

            # 先提取图片信息，获取图片总数
            images = self.image_processor.extract_images_from_soup(soup, url, excluded_formats)
            total_images = len(images)

            if total_images == 0:
                logger.warning(f"未找到任何图片: {url}")
                self.download_error.emit(url, "未找到任何图片")
                return False

            # 创建包含图片总数的保存目录
            save_path = create_save_directory(save_dir, title, download_count, total_images)

            # 使用线程池并发下载图片
            successful_downloads = 0
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 创建下载任务
                future_to_image = {
                    executor.submit(
                        self.download_image,
                        img_url,
                        save_path,
                        index,
                        total_images,
                        save_format
                    ): (index, img_url)
                    for index, (img_url, _) in enumerate(images, 1)
                }

                # 处理完成的任务
                for future in as_completed(future_to_image):
                    index, img_url = future_to_image[future]
                    try:
                        if future.result():
                            successful_downloads += 1
                    except Exception as e:
                        logger.error(f"下载任务失败: {img_url}, 错误: {str(e)}")
                    finally:
                        self.update_progress(url, total_images)

            if successful_downloads > 0:
                logger.info(f"下载完成: {url} -> {save_path} (成功: {successful_downloads}/{total_images})")
                self.download_completed.emit(url, save_path)
                return True
            else:
                logger.error(f"所有图片下载失败: {url}")
                self.download_error.emit(url, "所有图片下载失败")
                return False

        except Exception as e:
            logger.error(f"下载过程发生错误: {url}, 错误: {str(e)}")
            self.download_error.emit(url, str(e))
            return False
