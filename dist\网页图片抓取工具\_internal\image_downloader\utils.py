import os
import re
from urllib.parse import urljoin
from .logger import logger

def clean_filename(filename):
    """清理文件名中的非法字符"""
    return re.sub(r"[\/:*?\"<>|]", "_", filename)

def get_unique_save_path(base_path, download_count):
    """获取唯一的保存路径"""
    if download_count <= 1:
        return base_path

    # 如果已经下载过，添加序号
    dir_name = os.path.basename(base_path)
    parent_dir = os.path.dirname(base_path)
    new_dir_name = f"{dir_name}_{download_count}"
    new_path = os.path.join(parent_dir, new_dir_name)
    logger.debug(f"生成新的保存路径: {new_path}")
    return new_path

def extract_url_from_text(url):
    """从文本中提取URL"""
    if "：" in url:
        _, url = url.split("：", 1)
        url = url.strip().rstrip("；")
    return url

def make_absolute_url(base_url, relative_url):
    """将相对URL转换为绝对URL"""
    return urljoin(base_url, relative_url)

def create_save_directory(save_dir, title, download_count=1, total_images=None):
    """创建保存目录"""
    # 如果提供了图片总数，在目录名中包含图片数量信息
    if total_images is not None and total_images > 0:
        title_with_count = f"{title}（{total_images}张）"
    else:
        title_with_count = title

    base_save_path = os.path.join(save_dir, title_with_count)
    save_path = get_unique_save_path(base_save_path, download_count)
    os.makedirs(save_path, exist_ok=True)
    logger.info(f"创建保存目录: {save_path}")
    return save_path
