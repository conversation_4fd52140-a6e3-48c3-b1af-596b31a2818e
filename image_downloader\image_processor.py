import io
from PIL import Image
from .logger import logger

class ImageProcessor:
    @staticmethod
    def convert_format(image_data, target_format):
        """转换图片格式"""
        try:
            # 从二进制数据创建图片对象
            img = Image.open(io.BytesIO(image_data))

            # 如果是PNG格式且需要转换为JPG，需要处理透明背景
            if target_format.lower() == "jpg" and img.mode in ("RGBA", "LA"):
                logger.debug("处理PNG透明背景")
                background = Image.new("RGB", img.size, (255, 255, 255))
                background.paste(img, mask=img.split()[-1])
                img = background

            # 转换为RGB模式（如果需要）
            if img.mode not in ("RGB", "L"):
                logger.debug(f"转换图片模式从 {img.mode} 到 RGB")
                img = img.convert("RGB")

            # 保存为新的格式
            output = io.BytesIO()
            save_format = target_format.upper()
            if save_format == "JPG":
                save_format = "JPEG"
            img.save(output, format=save_format, quality=95)
            logger.debug(f"图片格式转换成功: {save_format}")
            return output.getvalue()
        except Exception as e:
            logger.error(f"转换图片格式失败: {str(e)}")
            return None

    @staticmethod
    def extract_images_from_soup(soup, base_url, excluded_formats):
        """从BeautifulSoup对象中提取图片URL，保持DOM顺序并去重"""
        images = []
        seen_urls = set()  # 用于去重

        # 只查找img标签，避免重复计算
        for img in soup.find_all('img'):
            img_url = img.get('src') or img.get('data-src') or img.get('data-original')
            if img_url:
                from .utils import make_absolute_url
                img_url = make_absolute_url(base_url, img_url)

                # 跳过已经处理过的URL
                if img_url in seen_urls:
                    logger.debug(f"跳过重复图片: {img_url}")
                    continue

                # 过滤掉明显不是图片的URL
                if not ImageProcessor._is_valid_image_url(img_url):
                    logger.debug(f"跳过非图片URL: {img_url}")
                    continue

                # 检查图片格式
                ext = img_url.split('.')[-1].lower().split('?')[0]  # 移除URL参数
                if ext in excluded_formats and excluded_formats[ext]:
                    logger.debug(f"跳过格式为 {ext} 的图片: {img_url}")
                    continue

                # 添加到已处理集合
                seen_urls.add(img_url)

                # 获取alt文本作为文件名
                alt_text = img.get('alt', '')
                images.append((img_url, alt_text))
                logger.debug(f"找到图片: {img_url} (alt: {alt_text})")

        logger.info(f"共找到 {len(images)} 张图片（已去重）")
        return images

    @staticmethod
    def _is_valid_image_url(url):
        """检查URL是否可能是图片"""
        # 过滤掉明显不是图片的URL
        invalid_patterns = [
            'yandex.ru/watch',  # 统计跟踪
            'google-analytics.com',  # 谷歌分析
            'facebook.com/tr',  # Facebook像素
            'doubleclick.net',  # 广告跟踪
            '.js',  # JavaScript文件
            '.css',  # CSS文件
            '.html',  # HTML文件
            '.xml',  # XML文件
        ]

        url_lower = url.lower()
        for pattern in invalid_patterns:
            if pattern in url_lower:
                return False

        # 检查是否包含常见图片格式
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg', '.tiff', '.ico']
        for ext in image_extensions:
            if ext in url_lower:
                return True

        # 如果没有明确的图片扩展名，但URL看起来像图片路径，也接受
        if any(keyword in url_lower for keyword in ['image', 'img', 'photo', 'pic', 'upload']):
            return True

        return False
